'use strict';

///////////////////////////////////////
// Modal window

const modal = document.querySelector('.modal');
const overlay = document.querySelector('.overlay');
const btnCloseModal = document.querySelector('.btn--close-modal');
const btnsOpenModal = document.querySelectorAll('.btn--show-modal');
// const nav__links = document.querySelector('.nav__links');

const openModal = function () {
  modal.classList.remove('hidden');
  overlay.classList.remove('hidden');
};

const closeModal = function () {
  modal.classList.add('hidden');
  overlay.classList.add('hidden');
};

for (let i = 0; i < btnsOpenModal.length; i++)
  btnsOpenModal[i].addEventListener('click', openModal);

btnCloseModal.addEventListener('click', closeModal);
overlay.addEventListener('click', closeModal);

document.addEventListener('keydown', function (e) {
  if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
    closeModal();
  }
});

document
  .querySelector('.btn--scroll-to')
  .addEventListener('click', function (e) {
    document
      .querySelector('#section--1')
      .scrollIntoView({ behavior: 'smooth' });
  });

document.querySelector('.nav__links').addEventListener('click', function (e) {
  e.preventDefault();
  if (e.target.classList.contains('nav__link')) {
    const id = e.target.getAttribute('href');
    document.querySelector(id).scrollIntoView({ behavior: 'smooth' });
  }
});

// //is not enough to select the entire document because this is not the real dom element if you want to apply the css in the document then you have to select the document element
// console.log(document.documentElement);

// console.log(document.head);
// console.log(document.body);

// const header = document.querySelector('.header');
// const allSections = document.querySelectorAll('.section');
// console.log(allSections);
// //this will reutrn the node list

// document.getElementById('section--1');
// const allButtons = document.getElementsByTagName('button');

// console.log(allButtons);
// // document.getElementsByClassName('btn');
// //message is the dom oject
// const message = document.createElement('div');
// message.classList.add('cookie-message');
// // message.textContent = 'We use cookies for improved functionality and analytics.';

// message.innerHTML =
//   'We use cookies for improved functionality and analytics. <button class="btn btn--close-cookie">Got it!</button>';

// header.append(message);
// // header.prepend(message);
// // //

// // header.append(message.cloneNode(true));

// document
//   .querySelector('.btn--close-cookie')
//   .addEventListener('click', function () {
//     message.remove();
//   });

// message.style.backgroundColor = '#37383d';
// message.style.width = '50%';

// console.log(message.style.height); //this will not work because style proopety only work for inline style
// console.log(message.style.backgroundColor);

// console.log(getComputedStyle(message)); //this will return the alll the properties which are inbuilt in the css file

// console.log(getComputedStyle(message).color);

// console.log(getComputedStyle(message).height);

// message.style.height =
//   Number.parseFloat(getComputedStyle(message).height, 10) + 20 + 'px';

// document.documentElement.style.setProperty('--color-primary', 'orangered');

// const logo = document.querySelector('.nav__logo');
// console.log(logo.alt);
// console.log(logo.src); //this is the absolute url

// console.log(logo.className);
// logo.alt = 'Beautiful minimalist logo'; //you can change the alternate variable value

// //non standard
// console.log(logo.designer); //this will not work because this is not the property of the logo
// console.log(logo.getAttribute('designer'));
// logo.setAttribute('company', 'Bankist');

// console.log(logo.getAttribute('src'));

// const link = document.querySelector('.twitter-link');
// console.log(link.href);
// console.log(link.getAttribute('href'));

// console.log(logo.dataset.versionNumber);

// const btnScrollTo = document.querySelector('.btn--scroll-to');
// const section1 = document.querySelector('#section--1');

// btnScrollTo.addEventListener('click', function (e) {
//   const s1coords = section1.getBoundingClientRect(); //this gives the position of the section1 relative to the viewport which means the position of the section1 relative to the viewport
//   console.log(s1coords);

//   console.log(e.target.getBoundingClientRect());
//   console.log('Current scroll (X/Y)' + window.pageXOffset, window.pageYOffset); //this gives the current scroll position means how much we have scrolled from the top
//   console.log(
//     'height/width viewport' + document.documentElement.clientHeight,
//     document.documentElement.clientWidth //this gives the height and width of the current rectangle which is visible to ths user
//   );
//   // window.scrollTo(s1coords.left, s1coords.top);// in this problem occurs that when we move upward of the page so s1coords.top is not the actual position of the section1 so to solve this problem we have to add the current scroll position to the s1coords.top

//   // window.scrollTo({
//   //   left: s1coords.left + window.pageXOffset,
//   //   top: s1coords.top + window.pageYOffset,
//   //   behavior: 'smooth', // for smooth scrolling
//   // });

//   // window.scrollTo({
//   //   left: s1coords.left + window.pageXOffset,
//   //   top: s1coords.top + window.pageYOffset,
//   //   behavior: 'smooth',
//   // });
//   section1.scrollIntoView({ behavior: 'smooth' }); //this is the new way of doing the smooth scrolling we didn't have to calculate the current scroll position so it's going to be easy to make these changes
// });

// const h1 = document.querySelector('h1');
// // h1.addEventListener('mouseenter', function (e) {
// //   //when we move the mouse to that element then this event will be triggered
// //   alert('addEventListener: Great! You are reading the heading :D');
// // });

// // h1.onmouseenter = function (e) {
// //   alert('onmouseenter: Great! You are reading the heading :D');
// // };

// const alerth1 = function (e) {
//   alert('addEventListener: Great! You are reading the heading :D');
//   // h1.removeEventListener('mouseenter', alerth1); //this will remove the event listener after ruuning once
// };

// h1.addEventListener('mouseenter', alerth1);

// setTimeout(() => h1.removeEventListener('mouseenter', alerth1), 10000); //in this case the event listener will be removed after 3 seconds

// // Why addEventListener is better as compared to direct assigning a function with the property
// // Because it allows multiple handlers, better separation of concerns, removal of handlers, and is the standard practice in modern JavaScript.

// //there is the another way of doing the event handling which is the old way of doing the event handling which is the using evnt listner to the html element as an attribute

// const randomInt = (min, max) =>
//   Math.floor(Math.random() * (max - min + 1) + min);

// const randomColor = () =>
//   `rgb(${randomInt(0, 255)},${randomInt(0, 255)},${randomInt(0, 255)})`;

// document.querySelector('.nav__link').addEventListener('click', function (e) {
//   this.style.backgroundColor = randomColor();
//   console.log('LINK', e.target, e.currentTarget);
//   console.log(e.currentTarget === this);

//   // Stop propagation
//   // e.stopPropagation(); //it's not a good idea but it's good to know about it because sometimes you will need this and sometimes when you are havinng the complex appliactioin then you should apply it to the parent element
// });

// document.querySelector('.nav__links').addEventListener(
//   'click',
//   function (e) {
//     this.style.backgroundColor = randomColor();
//     console.log('CONTAINER', e.target, e.currentTarget);
//   }
//   // true
// );

// // document.querySelector('.nav').addEventListener(
// //   'click',
// //   function (e) {
// //     this.style.backgroundColor = randomColor();
// //     console.log('NAV', e.target, e.currentTarget);
// //   }
// //   // true
// // );

// //the addEventListener are listening the events in the bubbling phase not in capturing phase that is the default behavior of the addEventListener method

// const h1 = document.querySelector('h1');

// console.log(h1);
// console.log(h1.querySelectorAll('.highlight'));
// //querySelector mai sirf ek class return hogi jo h1 ke andar hogi and the returns null if the class is not in the h1 element
// //querySelectorAll mai sare element return honge jo h1 ke andar hogi and the returns empty nodelist if the class is not in the h1 element

// console.log(h1.childNodes);
// console.log(h1.children); //this will return the direct children of the h1 element it returns the html collection which is live and the querySelectorAll returns the node list which is not live

// h1.firstElementChild.style.color = 'white';
// h1.lastElementChild.style.color = 'orangered';

// //Going UPwards: parents
// console.log(h1.parentNode);
// console.log(h1.parentElement);

// h1.closest('.header').style.background = 'var(--gradient-primary)';
// h1.closest('h1').style.background = 'var(--gradient-secondary)';

// console.log(h1.previousElementSibling);
// console.log(h1.nextElementSibling);
// console.log(h1.previousSibling);
// console.log(h1.nextSibling);

// console.log(h1.parentElement.children);
// [...h1.parentElement.children].forEach(function (el) {
//   if (el !== h1) el.style.transform = 'scale(0.5)';
// });
